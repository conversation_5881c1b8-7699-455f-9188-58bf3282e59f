{"version": 4, "terraform_version": "1.12.2", "serial": 4, "lineage": "cef6bb83-f179-7be4-ec3f-85b2a9980110", "outputs": {}, "resources": [{"mode": "managed", "type": "aws_instance", "name": "minimal_ec2", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"ami": "ami-0e2fdb449dd2f51fe", "arn": "arn:aws:ec2:ap-southeast-5:084519755969:instance/i-080668b23c1057418", "associate_public_ip_address": true, "availability_zone": "ap-southeast-5c", "capacity_reservation_specification": [{"capacity_reservation_preference": "open", "capacity_reservation_target": []}], "cpu_core_count": 1, "cpu_options": [{"amd_sev_snp": "", "core_count": 1, "threads_per_core": 2}], "cpu_threads_per_core": 2, "credit_specification": [{"cpu_credits": "unlimited"}], "disable_api_stop": false, "disable_api_termination": false, "ebs_block_device": [], "ebs_optimized": false, "enable_primary_ipv6": null, "enclave_options": [{"enabled": false}], "ephemeral_block_device": [], "get_password_data": false, "hibernation": false, "host_id": "", "host_resource_group_arn": null, "iam_instance_profile": "", "id": "i-080668b23c1057418", "instance_initiated_shutdown_behavior": "stop", "instance_lifecycle": "", "instance_market_options": [], "instance_state": "running", "instance_type": "t3.micro", "ipv6_address_count": 0, "ipv6_addresses": [], "key_name": "aws", "launch_template": [], "maintenance_options": [{"auto_recovery": "default"}], "metadata_options": [{"http_endpoint": "enabled", "http_protocol_ipv6": "disabled", "http_put_response_hop_limit": 1, "http_tokens": "optional", "instance_metadata_tags": "disabled"}], "monitoring": false, "network_interface": [], "outpost_arn": "", "password_data": "", "placement_group": "", "placement_partition_number": 0, "primary_network_interface_id": "eni-0e1a4454cf913b71e", "private_dns": "ip-172-31-40-233.ap-southeast-5.compute.internal", "private_dns_name_options": [{"enable_resource_name_dns_a_record": false, "enable_resource_name_dns_aaaa_record": false, "hostname_type": "ip-name"}], "private_ip": "*************", "public_dns": "ec2-43-217-199-230.ap-southeast-5.compute.amazonaws.com", "public_ip": "**************", "root_block_device": [{"delete_on_termination": true, "device_name": "/dev/sda1", "encrypted": false, "iops": 100, "kms_key_id": "", "tags": {}, "tags_all": {}, "throughput": 0, "volume_id": "vol-0ce6f73c0a9723f2c", "volume_size": 8, "volume_type": "gp2"}], "secondary_private_ips": [], "security_groups": ["allow_ssh_http"], "source_dest_check": true, "spot_instance_request_id": "", "subnet_id": "subnet-05d98f1e4b969665b", "tags": {"Name": "MinimalEC2Instance"}, "tags_all": {"Name": "MinimalEC2Instance"}, "tenancy": "default", "timeouts": null, "user_data": null, "user_data_base64": null, "user_data_replace_on_change": false, "volume_tags": null, "vpc_security_group_ids": ["sg-07eb72464e9b30e23"]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMCwicmVhZCI6OTAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_security_group.allow_ssh_http"]}]}, {"mode": "managed", "type": "aws_security_group", "name": "allow_ssh_http", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-southeast-5:084519755969:security-group/sg-07eb72464e9b30e23", "description": "Allow SSH and HTTP inbound traffic", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-07eb72464e9b30e23", "ingress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 22, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 22}, {"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 80, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 80}], "name": "allow_ssh_http", "name_prefix": "", "owner_id": "084519755969", "revoke_rules_on_delete": false, "tags": {"Name": "allow_ssh_http"}, "tags_all": {"Name": "allow_ssh_http"}, "timeouts": null, "vpc_id": "vpc-018a7e26323740e9c"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0="}]}], "check_results": null}