# variables.tf

variable "region" {
  description = "AWS region"
  type        = string
  default     = "ap-southeast-5"
}

variable "vpc_id" {
  description = "VPC ID (optional, uses default VPC if not specified)"
  type        = string
  default     = null
}

variable "key_name" {
  description = "AWS key pair name for EC2 instance"
  type        = string
}

variable "ami_id" {
  description = "AMI ID for the EC2 instance"
  type        = string
}
