# main.tf

provider "aws" {
  region = var.region
}

resource "aws_security_group" "allow_ssh_http" {
  name        = "allow_ssh_http"
  description = "Allow SSH and HTTP inbound traffic"
  vpc_id      = var.vpc_id # 可选：若需指定 VPC

  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "allow_ssh_http"
  }
}

resource "aws_instance" "minimal_ec2" {
  ami           = var.ami_id
  instance_type = "t3.micro"

  key_name = var.key_name # 替换为你自己的密钥对名称

  vpc_security_group_ids = [aws_security_group.allow_ssh_http.id]

  tags = {
    Name = "MinimalEC2Instance"
  }
}